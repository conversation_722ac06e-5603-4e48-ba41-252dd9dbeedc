<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>Assign02 - LoginB</title>
    <style type="text/css">
        * { box-sizing: border-box; }
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: #f5f7fb;
            color: #111827;
        }
        h1 { font-size: 18pt !important; color:#111827; }

        fieldset {
            padding: 16px 18px;
            border: 1px solid #e5e7eb;
            border-radius: 14px;
            width: 400px;
            overflow: auto;
            margin: 10px;
            background: #ffffff;
            box-shadow: 0 6px 18px rgba(0,0,0,.06);
        }
        legend {
            color: #111827;
            margin: 0 10px 0 0;
            padding: 0 6px;
            font-size: 12.5pt;
            font-weight: 700;
        }
        label span { color: #ef4444; font-weight:700; }

        fieldset#info { position:absolute; top: 60px; left: 20px; width: 460px; }
        fieldset#passwords { position:absolute; top: 60px; left: 520px; width: 260px; }
        #submit { position:absolute; top: 230px; left: 20px; width: 460px; text-align:center; border:none; background:transparent; box-shadow:none; padding:0; }
        #navigation { position:absolute; top: 290px; left: 20px; width: 460px; }
        #part3 { position:absolute; top: 390px; left: 20px; width: 460px; }

        ul { list-style: none; margin: 8px 0 0; padding:0; }
        ul li { display:block; width:100%; clear:both; padding: 6px 0; }
        ul li label {
            float:left; width:38%;
            padding: 10px 0;
            color:#374151;
            font-weight:600;
        }
        ul li input {
            float:right; width:60%;
            margin:0;
            padding:10px 12px;
            border:1px solid #d1d5db;
            border-radius:10px;
            background:#fff;
            font-size:14px;
            transition: border-color .15s ease, box-shadow .15s ease;
        }
        li input:focus {
            border-color:#6366f1;
            box-shadow: 0 0 0 3px rgba(99,102,241,.25);
            outline:none;
        }

        #SubmitBtn {
            display:inline-block;
            background:#4f46e5;
            color:#ffffff;
            border: none;
            padding: 10px 18px;
            border-radius:999px;
            font-weight:700;
            cursor:pointer;
            box-shadow: 0 8px 22px rgba(79,70,229,.25);
            transition: transform .04s ease, filter .15s ease, box-shadow .2s ease;
        }
        #SubmitBtn:hover { filter: brightness(1.05); }
        #SubmitBtn:active { transform: translateY(1px); }

        #passwords .label-text { display:inline-block; min-width:78px; color:#4338ca; font-weight:700; }
        #passwords { background: linear-gradient(180deg, #ffffff 0%, #fafbff 100%); }
        #passwords br { line-height: 1.0; }

        #navigation .nav-content{ text-align:center; font-weight:600; }
        #navigation a{
            display:inline-block;
            padding:8px 14px;
            color:#4338ca;
            background:#eef2ff;
            border:1px solid #e0e7ff;
            border-radius:999px;
            text-decoration:none;
        }
        #navigation a:hover{ background:#e0e7ff; }

        #part3 p { margin: 0; color:#374151; font-size: 14px; }

        @media (max-width: 860px){
            fieldset#info, fieldset#passwords, #submit, #navigation, #part3{ position:static; width:auto; }
            h1{ text-indent: 0 !important; text-align:center; }
        }
    </style>
</head>
<body>
    <h1 style="font-size: 14pt; text-indent: 360px;">Assign02 - LoginB</h1>

    <form name="form0" id="form0" action="loginB.php" method="post">
        <fieldset id="info">
            <legend>Login</legend>
            <ul>
                <li>
                    <label for="userID">User ID <span>*</span></label>
                    <input type="text" name="userID" id="userID" size="30" maxlength="30" />
                </li>
                <li>
                    <label for="passwd">Password <span>*</span></label>
                    <input type="password" name="passwd" id="passwd" size="30" maxlength="30" />
                </li>
            </ul>
        </fieldset>
        <fieldset id="passwords">
            <legend>Passwords</legend>
            <span class="label-text">User ID:</span> page1b<br>
            <span class="label-text">Password:</span> messi<br><br>
            <span class="label-text">User ID:</span> page2b<br>
            <span class="label-text">Password:</span> ronaldo<br><br>
            <span class="label-text">User ID:</span> page3b<br>
            <span class="label-text">Password:</span> neymar<br><br>
            <span class="label-text">User ID:</span> page4b (Original Index)<br>
            <span class="label-text">Password:</span> mbappe<br><br>
        </fieldset>
        <fieldset id="submit">
            <input type="submit" id="SubmitBtn" name="SubmitBtn" value="Login" />
        </fieldset>
        <fieldset id="navigation">
            <legend>Navigation</legend>
            <div class="nav-content">
                <a href="index.php">Back to Original Index</a>
            </div>
        </fieldset>
        <fieldset id="part3">
            <legend>Part 3</legend>
            <p>Please read the README file to understand how I used AI for part 3.</p>
        </fieldset>
    </form>

    <script>
        document.getElementById("userID").focus();
    </script>

</body>
</html>
